from django.db import models
from django.db.models import OuterRef, Subquery, Sum, DecimalField, F
from django.db.models.functions import Coalesce


class TransactionOrderQuerySet(models.QuerySet):
    def with_offset_fields(self):
        model = self.model
        relation_model = getattr(model, 'OffsetRelationModel', None)
        action = getattr(model, 'transaction_action', None)
        if not relation_model or not action:
            return self

        if action == '销售':
            filter_field = 'sale_order'
        if action == '回购':
            filter_field = 'buyback_order'

        subquery = (
            relation_model.objects
            .filter(**{filter_field: OuterRef('pk')})
            .values(filter_field)
            .annotate(total=Sum('offset_weight'))
            .values('total')
        )

        return self.annotate(
            offset_weight_annotated=Coalesce(
                Subquery(subquery, output_field=DecimalField(max_digits=12, decimal_places=3)),
                0,
                output_field=DecimalField(max_digits=12, decimal_places=3)
            ),
            holding_weight_annotated=F('weight') - F('offset_weight_annotated'),
            holding_amount_annotated=F('locked_price') * (F('weight') - F('offset_weight_annotated'))
        )
