from decimal import Decimal

from django.db import models
from django.db.models import F, DecimalField, Sum, Q
from django.core.exceptions import ValidationError

from core.model_managers import TransactionOrderManager


class TransactionAgency(models.Model):
    class Meta:
        verbose_name = '交易代理机构'
        verbose_name_plural = '交易代理机构'

    name = models.CharField(
        max_length=128,
        verbose_name='名称',
    )

    def __str__(self):
        return self.name


class TransactionUser(models.Model):
    class Meta:
        verbose_name = '交易用户'
        verbose_name_plural = '交易用户'

    name = models.CharField(
        max_length=128,
        verbose_name='名称',
    )

    agency = models.ForeignKey(
        to=TransactionAgency,
        on_delete=models.PROTECT,
        verbose_name='交易代理机构',
    )

    monthly_rebate_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        verbose_name='月结返点系数',
    )

    daily_deduction_factor = models.DecimalField(
        max_digits=3,
        decimal_places=2,
        verbose_name='每日账扣系数',
    )

    xau_margin_factor = models.PositiveIntegerField(
        verbose_name='黄金保证金系数',
        help_text='每 1kg 黄金需要缴纳的保证金'
    )

    xpt_margin_factor = models.PositiveIntegerField(
        verbose_name='铂金保证金系数',
        help_text='每 1kg 铂金需要缴纳的保证金'
    )

    xau_over_night_fee_factor = models.PositiveIntegerField(
        verbose_name='黄金过夜费系数',
        help_text='每 1kg 黄金 1 天所需要缴纳的过夜费'
    )

    xpt_over_night_fee_factor = models.PositiveIntegerField(
        verbose_name='铂金过夜费系数',
        help_text='每 1kg 铂金 1 天所需要缴纳的过夜费'
    )

    def get_xau_holding_orders(self, *args, **kwargs):
        return self._meta.model.objects.filter(trans_user=self, *args, **kwargs)

    def __str__(self):
        return self.name


class TransactionOrder(models.Model):
    class Meta:
        abstract = True
        base_manager_name = 'objects'
        default_manager_name = 'objects'

    OffsetRelationModel = None
    transaction_action = None

    objects = TransactionOrderManager()

    trans_user = models.ForeignKey(
        to=TransactionUser,
        on_delete=models.PROTECT,
        verbose_name='交易用户',
    )

    trans_date = models.DateField(
        verbose_name='交易日期',
    )

    locked_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='锁定价格',
    )

    weight = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='重量',
        help_text='以 g 为单位',
    )

    trans_amount = models.GeneratedField(
        expression=F('locked_price') * F('weight'),
        output_field=DecimalField(max_digits=18, decimal_places=3),
        db_persist=True,
        verbose_name='交易金额',
    )

    @property
    def offset_weight(self):
        return self.__dict__.get('offset_weight_annotated') or self._calc_offset_weight()

    def _calc_offset_weight(self):
        relation_model = getattr(self, 'OffsetRelationModel', None)
        action = getattr(self, 'transaction_action', None)
        if not relation_model or not self.pk or not action:
            return Decimal(0)
        filter_field = 'sale_order' if action == '销售' else 'buyback_order'
        total = relation_model.objects.filter(**{filter_field: self}).aggregate(
            total=Sum('offset_weight')
        )['total']
        return total or Decimal(0)

    @property
    def holding_weight(self):
        return self.__dict__.get('holding_weight_annotated') or (
            (self.weight or Decimal(0)) - (self.offset_weight or Decimal(0))
        )

    @property
    def holding_amount(self):
        return self.__dict__.get('holding_amount_annotated') or (
            (self.holding_weight or Decimal(0)) * (self.locked_price or Decimal(0))
        )

    def __str__(self):
        return (
            f'{self.trans_user.name} {self.trans_date} {self.transaction_action} '
            f'{self.weight}g {self.locked_price} '
            f'(已抵消 {self.offset_weight}g, 剩余 {self.holding_weight}g)'
        )


class XAUOffsetRelation(models.Model):
    """黄金抵消关系表 - 支持重量拆分"""

    class Meta:
        verbose_name = '黄金抵消关系'
        verbose_name_plural = '黄金抵消关系'
        ordering = ['-created_at']

    sale_order = models.ForeignKey(
        to='XAUSaleOrder',
        on_delete=models.CASCADE,
        verbose_name='销售订单',
        related_name='offset_relations'
    )

    buyback_order = models.ForeignKey(
        to='XAUBuyBackOrder',
        on_delete=models.CASCADE,
        verbose_name='回购订单',
        related_name='offset_relations'
    )

    offset_weight = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='抵消重量',
        help_text='以 g 为单位'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    def clean(self):
        """验证抵消关系的有效性"""
        from django.core.exceptions import ValidationError

        if self.sale_order and self.buyback_order:
            # 检查用户是否一致
            if self.sale_order.trans_user != self.buyback_order.trans_user:
                raise ValidationError('抵消的销售订单和回购订单必须属于同一用户')

            if self.offset_weight is None:
                raise ValidationError('抵消重量不能为空')

            # 检查抵消重量必须大于0
            if self.offset_weight <= 0:
                raise ValidationError('抵消重量必须大于0')

            # 只有当订单都已保存时才进行剩余重量验证
            if self.sale_order.pk and self.buyback_order.pk:
                # 检查抵消重量不能超过销售订单剩余重量
                sale_holding_weight = self.sale_order.holding_weight
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XAUOffsetRelation.objects.get(pk=self.pk).offset_weight
                    sale_holding_weight += current_offset

                if self.offset_weight > sale_holding_weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过销售订单剩余重量 ({sale_holding_weight}g)'
                    )

                # 检查抵消重量不能超过回购订单剩余重量
                buyback_holding_weight = self.buyback_order.holding_weight
                if hasattr(self, 'pk') and self.pk:
                    # 编辑现有记录时，需要加回当前记录的重量
                    current_offset = XAUOffsetRelation.objects.get(pk=self.pk).offset_weight
                    buyback_holding_weight += current_offset

                if self.offset_weight > buyback_holding_weight:
                    raise ValidationError(
                        f'抵消重量 ({self.offset_weight}g) 不能超过回购订单剩余重量 ({buyback_holding_weight}g)'
                    )
            else:
                raise ValidationError('请先保存订单再进行抵消操作')

    def __str__(self):
        return f'销售#{self.sale_order.id} ↔ 回购#{self.buyback_order.id} ({self.offset_weight}g)'


class XAUTransactionOrder(TransactionOrder):
    ...


class XAUSaleOrder(XAUTransactionOrder):
    class Meta:
        verbose_name = '黄金销售订单'
        verbose_name_plural = '黄金销售订单'

    OffsetRelationModel = XAUOffsetRelation

    transaction_action = '销售'


class XAUBuyBackOrder(XAUTransactionOrder):
    class Meta:
        verbose_name = '黄金回购订单'
        verbose_name_plural = '黄金回购订单'

    OffsetRelationModel = XAUOffsetRelation

    transaction_action = '回购'


class DailyBill(models.Model):
    class Meta:
        verbose_name = '账单'
        verbose_name_plural = '账单'
        unique_together = ['transaction_user', 'bill_start_date', 'bill_end_date']
        ordering = ['-bill_end_date', 'transaction_user__name']

    transaction_user = models.ForeignKey(
        to=TransactionUser,
        on_delete=models.PROTECT,
        verbose_name='交易用户',
    )

    bill_start_date = models.DateField(
        verbose_name='账单开始日期',
        help_text='账单周期的开始日期（包含）'
    )

    bill_end_date = models.DateField(
        verbose_name='账单结束日期',
        help_text='账单周期的结束日期（包含）'
    )

    xau_internal_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='黄金内部收盘价',
        help_text='每克黄金的内部收盘价格，用于市值计算'
    )

    xau_external_closing_price = models.DecimalField(
        max_digits=12,
        decimal_places=3,
        verbose_name='黄金外部收盘价',
        help_text='每克黄金的市场收盘价格'
    )

    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='创建时间'
    )

    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='更新时间'
    )

    def __str__(self):
        return f'{self.transaction_user.name} - {self.bill_start_date} 至 {self.bill_end_date}'

    def clean(self):
        """验证账单日期"""
        if self.bill_start_date and self.bill_end_date:
            if self.bill_start_date > self.bill_end_date:
                raise ValidationError('账单开始日期不能晚于结束日期')

    @property
    def bill_period_days(self):
        """账单周期天数"""
        return (self.bill_end_date - self.bill_start_date).days + 1

    def calculate_daily_holding_weight(self, target_date):
        """计算指定日期的总持仓重量（用于过夜费计算）"""
        from datetime import timedelta

        # 获取目标日期及之前的所有销售订单
        sales_before_date = XAUSaleOrder.objects.filter(
            trans_user=self.transaction_user,
            trans_date__lte=target_date,
        )

        # 获取目标日期及之前的所有回购订单
        buybacks_before_date = XAUBuyBackOrder.objects.filter(
            trans_user=self.transaction_user,
            trans_date__lte=target_date,
        )

        # 计算销售订单在目标日期的未抵消重量
        total_sale_weight = Decimal(0)
        for sale in sales_before_date:
            sale_weight = sale.weight
            # 只减去在目标日期当天或之前创建的抵消关系
            # 抵消关系只从创建日期开始生效，不追溯
            offset_relations = sale.offset_relations.filter(
                created_at__date__lte=target_date
            )
            offset_weight = sum(rel.offset_weight for rel in offset_relations)
            effective_weight = sale_weight - offset_weight
            if effective_weight > 0:
                total_sale_weight += effective_weight

        # 计算回购订单在目标日期的未抵消重量
        total_buyback_weight = Decimal(0)
        for buyback in buybacks_before_date:
            buyback_weight = buyback.weight
            # 只减去在目标日期当天或之前创建的抵消关系
            # 抵消关系只从创建日期开始生效，不追溯
            offset_relations = buyback.offset_relations.filter(
                created_at__date__lte=target_date
            )
            offset_weight = sum(rel.offset_weight for rel in offset_relations)
            effective_weight = buyback_weight - offset_weight
            if effective_weight > 0:
                total_buyback_weight += effective_weight

        # 总持仓重量 = 销售未抵消重量 + 回购未抵消重量
        # 因为销售和回购都需要支付过夜费
        return total_sale_weight + total_buyback_weight

    @property
    def xau_holding_weight(self):
        """账单周期结束时的净持仓重量"""
        return self.calculate_daily_holding_weight(self.bill_end_date)

    @property
    def xau_overnight_fee(self):
        """黄金过夜费 - 按每日实际持仓计算"""
        from datetime import timedelta

        total_fee = Decimal(0)
        current_date = self.bill_start_date

        while current_date <= self.bill_end_date:
            daily_holding_weight = self.calculate_daily_holding_weight(current_date)
            if daily_holding_weight > 0:
                weight_kg = daily_holding_weight / Decimal(1000)
                daily_fee = weight_kg * self.transaction_user.xau_over_night_fee_factor
                total_fee += daily_fee

            current_date += timedelta(days=1)

        return total_fee

    def get_unsettle_sale_orders(self):
        """获取未结算的销售订单（holding_weight_annotated 不为 0）"""
        return XAUSaleOrder.objects.filter(
            trans_user=self.transaction_user,
        ).exclude(holding_weight_annotated=0)

    @property
    def unsettle_sale_orders(self):
        """获取所有未结算的销售订单（holding_weight_annotated 不为 0）"""
        return XAUSaleOrder.objects.filter(
            trans_user=self.transaction_user,
        ).exclude(holding_weight_annotated=0)

    @property
    def unsettle_buyback_orders(self):
        """获取所有未结算的回购订单（holding_weight_annotated 不为 0）"""
        return XAUBuyBackOrder.objects.filter(
            trans_user=self.transaction_user,
        ).exclude(holding_weight_annotated=0)

    @property
    def settled_sale_orders(self):
        """获取所有已结算的销售订单（holding_weight_annotated = 0）"""
        return XAUSaleOrder.objects.filter(
            trans_user=self.transaction_user,
        ).filter(holding_weight_annotated=0)

    @property
    def settled_buyback_orders(self):
        """获取所有已结算的回购订单（holding_weight_annotated = 0）"""
        return XAUBuyBackOrder.objects.filter(
            trans_user=self.transaction_user,
        ).filter(holding_weight_annotated=0)
