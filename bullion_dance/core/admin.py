from django.contrib import admin
from .models import (
    TransactionAgency,
    TransactionUser,
    XAUSaleOrder,
    XAUBuyBackOrder,
    XAUOffsetRelation,
    DailyBill,
)


@admin.register(TransactionAgency)
class TransactionAgencyAdmin(admin.ModelAdmin):
    list_display = ['name']
    search_fields = ['name']


@admin.register(TransactionUser)
class TransactionUserAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'agency',
        'monthly_rebate_factor',
        'daily_deduction_factor',
        'xau_margin_factor',
        'xpt_margin_factor',
    ]
    readonly_fields = []
    search_fields = ['name', 'agency__name']
    list_filter = ['agency']


class TransactionOrderAdmin(admin.ModelAdmin):
    def display_offset_status(self, obj):
        """显示抵消状态"""
        if obj.offset_weight > 0:
            if obj.offset_weight >= obj.weight:
                return "完全抵消"
            else:
                return "部分抵消"
        return "未抵消"

    display_offset_status.short_description = '抵消状态'

    def display_holding_weight(self, obj):
        return f"{obj.holding_weight:.3f}"

    display_holding_weight.short_description = '剩余重量'

    def display_holding_amount(self, obj):
        return f"{obj.holding_amount:.3f}"

    display_holding_amount.short_description = '剩余金额'

    list_display = [
        'trans_user',
        'trans_date',
        'locked_price',
        'weight',
        'trans_amount',
        'display_offset_status',
        'display_holding_weight',
        'display_holding_amount',
    ]
    fields = [
        'trans_user',
        'trans_date',
        'locked_price',
        'weight',
        'trans_amount',
        'display_offset_status',
        'display_holding_weight',
        'display_holding_amount',
    ]
    readonly_fields = [
        'trans_amount',
        'display_offset_status',
        'display_holding_weight',
        'display_holding_amount',
    ]
    search_fields = [
        'trans_user__name'
    ]
    list_filter = [
        'trans_date'
    ]


class XAUOffsetRelationInline(admin.TabularInline):
    model = XAUOffsetRelation
    fk_name = 'sale_order'
    extra = 0
    fields = ['buyback_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XAUSaleOrder)
class XAUSaleOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XAUOffsetRelationInline]


class XAUOffsetRelationInlineBuyback(admin.TabularInline):
    model = XAUOffsetRelation
    fk_name = 'buyback_order'
    extra = 0
    fields = ['sale_order', 'offset_weight', 'created_at']
    readonly_fields = ['created_at']


@admin.register(XAUBuyBackOrder)
class XAUBuyBackOrderAdmin(TransactionOrderAdmin):
    list_display = TransactionOrderAdmin.list_display
    readonly_fields = TransactionOrderAdmin.readonly_fields
    inlines = [XAUOffsetRelationInlineBuyback]


@admin.register(XAUOffsetRelation)
class XAUOffsetRelationAdmin(admin.ModelAdmin):
    list_display = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'display_sale_holding',
        'display_buyback_holding',
        'created_at'
    ]

    list_filter = [
        'created_at',
        'sale_order__trans_user__agency',
    ]

    search_fields = [
        'sale_order__trans_user__name',
        'buyback_order__trans_user__name',
        'sale_order__id',
        'buyback_order__id',
    ]

    readonly_fields = ['created_at']

    fields = [
        'sale_order',
        'buyback_order',
        'offset_weight',
        'created_at',
    ]

    def display_sale_holding(self, obj):
        """显示销售订单剩余重量"""
        holding = obj.sale_order.holding_weight
        return f"{holding:.3f} g"

    display_sale_holding.short_description = '销售订单剩余'

    def display_buyback_holding(self, obj):
        """显示回购订单剩余重量"""
        holding = obj.buyback_order.holding_weight
        return f"{holding:.3f} g"

    display_buyback_holding.short_description = '回购订单剩余'

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """优化外键字段的查询"""
        if db_field.name == "sale_order":
            kwargs["queryset"] = XAUSaleOrder.objects.select_related('trans_user')
        elif db_field.name == "buyback_order":
            kwargs["queryset"] = XAUBuyBackOrder.objects.select_related('trans_user')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


@admin.register(DailyBill)
class DailyBillAdmin(admin.ModelAdmin):
    list_display = [
        'transaction_user',
        'bill_start_date',
        'bill_end_date',
        'display_bill_period_days',
        'display_xau_holding_weight',
        'created_at',
    ]

    list_filter = [
        'bill_start_date',
        'bill_end_date',
        'transaction_user__agency',
        'created_at',
    ]

    search_fields = [
        'transaction_user__name',
        'transaction_user__agency__name',
    ]

    readonly_fields = [
        'display_bill_period_days',
        'display_xau_holding_weight',
        'display_xau_overnight_fee',
        'created_at',
        'updated_at',
    ]

    fields = [
        'transaction_user',
        'bill_start_date',
        'bill_end_date',
        ('xau_internal_closing_price', 'xau_external_closing_price'),
        'display_xau_holding_weight',
        'display_xau_overnight_fee',
        'created_at',
        'updated_at',
    ]

    date_hierarchy = 'bill_end_date'

    def display_xau_holding_weight(self, obj):
        """显示黄金过夜费计算重量"""
        return f"{obj.xau_holding_weight:.3f} g"

    display_xau_holding_weight.short_description = '黄金计费重量'

    def display_xau_overnight_fee(self, obj):
        """显示黄金过夜费"""
        return f"¥{obj.xau_overnight_fee:.2f}"

    display_xau_overnight_fee.short_description = '黄金过夜费'

    def display_bill_period_days(self, obj):
        """显示账单周期天数"""
        return f"{obj.bill_period_days} 天"

    display_bill_period_days.short_description = '周期天数'
