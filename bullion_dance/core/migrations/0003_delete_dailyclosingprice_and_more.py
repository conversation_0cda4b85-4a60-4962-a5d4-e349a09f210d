# Generated by Django 5.2.7 on 2025-11-05 08:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_xautransactionorder_locked_price_and_more'),
    ]

    operations = [
        migrations.DeleteModel(
            name='DailyClosingPrice',
        ),
        migrations.AddField(
            model_name='dailybill',
            name='xau_external_closing_price',
            field=models.DecimalField(decimal_places=3, default=0, help_text='每克黄金的市场收盘价格', max_digits=12, verbose_name='黄金外部收盘价'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dailybill',
            name='xau_internal_closing_price',
            field=models.DecimalField(decimal_places=3, default=0, help_text='每克黄金的内部收盘价格，用于市值计算', max_digits=12, verbose_name='黄金内部收盘价'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dailybill',
            name='xpt_external_closing_price',
            field=models.DecimalField(decimal_places=3, default=0, help_text='每克铂金的市场收盘价格', max_digits=12, verbose_name='铂金外部收盘价'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='dailybill',
            name='xpt_internal_closing_price',
            field=models.DecimalField(decimal_places=3, default=0, help_text='每克铂金的内部收盘价格，用于市值计算', max_digits=12, verbose_name='铂金内部收盘价'),
            preserve_default=False,
        ),
    ]
