# Generated by Django 5.2.7 on 2025-11-04 18:56

import django.db.models.deletion
import django.db.models.expressions
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='TransactionAgency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='名称')),
            ],
            options={
                'verbose_name': '交易代理机构',
                'verbose_name_plural': '交易代理机构',
            },
        ),
        migrations.CreateModel(
            name='TransactionOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trans_date', models.DateField(verbose_name='交易日期')),
                ('locked_price', models.DecimalField(decimal_places=3, max_digits=12, verbose_name='锁定价格')),
                ('weight', models.DecimalField(decimal_places=3, help_text='以 g 为单位', max_digits=12, verbose_name='重量')),
                ('trans_amount', models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(models.F('locked_price'), '*', models.F('weight')), output_field=models.DecimalField(decimal_places=3, max_digits=18), verbose_name='交易金额')),
            ],
        ),
        migrations.CreateModel(
            name='XAUBuyBackOrder',
            fields=[
                ('transactionorder_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.transactionorder')),
            ],
            options={
                'verbose_name': '黄金回购订单',
                'verbose_name_plural': '黄金回购订单',
            },
            bases=('core.transactionorder',),
        ),
        migrations.CreateModel(
            name='XAUSaleOrder',
            fields=[
                ('transactionorder_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.transactionorder')),
            ],
            options={
                'verbose_name': '黄金销售订单',
                'verbose_name_plural': '黄金销售订单',
            },
            bases=('core.transactionorder',),
        ),
        migrations.CreateModel(
            name='XPTBuyBackOrder',
            fields=[
                ('transactionorder_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.transactionorder')),
            ],
            options={
                'verbose_name': '铂金回购订单',
                'verbose_name_plural': '铂金回购订单',
            },
            bases=('core.transactionorder',),
        ),
        migrations.CreateModel(
            name='XPTSaleOrder',
            fields=[
                ('transactionorder_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='core.transactionorder')),
            ],
            options={
                'verbose_name': '铂金销售订单',
                'verbose_name_plural': '铂金销售订单',
            },
            bases=('core.transactionorder',),
        ),
        migrations.CreateModel(
            name='TransactionUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128, verbose_name='名称')),
                ('rebate_factor', models.DecimalField(decimal_places=2, max_digits=3, verbose_name='返点系数')),
                ('buyback_adjustment_factor', models.DecimalField(decimal_places=2, max_digits=3, verbose_name='回购下浮系数')),
                ('xau_margin_factor', models.PositiveIntegerField(help_text='每 1kg 黄金需要缴纳的保证金', verbose_name='黄金保证金系数')),
                ('xpt_margin_factor', models.PositiveIntegerField(help_text='每 1kg 铂金需要缴纳的保证金', verbose_name='铂金保证金系数')),
                ('xau_over_night_fee_factor', models.PositiveIntegerField(help_text='每 1kg 黄金 1 天所需要缴纳的过夜费', verbose_name='黄金过夜费系数')),
                ('xpt_over_night_fee_factor', models.PositiveIntegerField(help_text='每 1kg 铂金 1 天所需要缴纳的过夜费', verbose_name='铂金过夜费系数')),
                ('agency', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.transactionagency', verbose_name='交易代理机构')),
            ],
            options={
                'verbose_name': '交易用户',
                'verbose_name_plural': '交易用户',
            },
        ),
        migrations.AddField(
            model_name='transactionorder',
            name='trans_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.transactionuser', verbose_name='交易用户'),
        ),
        migrations.CreateModel(
            name='OverNightFeeOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('settle_date', models.DateField(verbose_name='结算日期')),
                ('transaction_user', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.transactionuser', verbose_name='交易用户')),
            ],
            options={
                'verbose_name': '过夜费订单',
                'verbose_name_plural': '过夜费订单',
            },
        ),
    ]
