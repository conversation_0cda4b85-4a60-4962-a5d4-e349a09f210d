# Generated by Django 5.2.7 on 2025-11-05 07:58

import django.db.models.deletion
import django.db.models.expressions
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='xautransactionorder',
            name='locked_price',
            field=models.DecimalField(decimal_places=3, max_digits=12, verbose_name='锁定价格'),
        ),
        migrations.AlterField(
            model_name='xautransactionorder',
            name='trans_amount',
            field=models.GeneratedField(db_persist=True, expression=django.db.models.expressions.CombinedExpression(models.F('locked_price'), '*', models.F('weight')), output_field=models.DecimalField(decimal_places=3, max_digits=18), verbose_name='交易金额'),
        ),
        migrations.AlterField(
            model_name='xautransactionorder',
            name='trans_date',
            field=models.DateField(verbose_name='交易日期'),
        ),
        migrations.AlterField(
            model_name='xautransactionorder',
            name='trans_user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='core.transactionuser', verbose_name='交易用户'),
        ),
        migrations.AlterField(
            model_name='xautransactionorder',
            name='weight',
            field=models.DecimalField(decimal_places=3, help_text='以 g 为单位', max_digits=12, verbose_name='重量'),
        ),
    ]
