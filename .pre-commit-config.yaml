exclude: '^docs/|/migrations/|devcontainer.json'
default_stages: [pre-commit]
minimum_pre_commit_version: "3.2.0"

default_language_version:
  python: python3.13

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v6.0.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-yaml
      - id: debug-statements
      - id: check-builtin-literals
      - id: check-case-conflict
      - id: check-docstring-first
      - id: detect-private-key

  - repo: https://github.com/adamchainz/django-upgrade
    rev: '1.29.1'
    hooks:
      - id: django-upgrade
        args: ['--target-version', '5.2']

  # Run the Ruff linter.
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.14.3
    hooks:
      # Linter
      - id: ruff-check
        args: [--fix, --exit-non-zero-on-fix]
      # Formatter
      - id: ruff-format

  - repo: https://github.com/Riverside-Healthcare/djLint
    rev: v1.36.4
    hooks:
      - id: djlint-reformat-django
      - id: djlint-django

# sets up .pre-commit-ci.yaml to ensure pre-commit dependencies stay up to date
ci:
  autoupdate_schedule: weekly
  skip: []
  submodules: false
