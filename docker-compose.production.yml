volumes:
  production_postgres_data: {}
  production_postgres_data_backups: {}
  production_traefik: {}
  production_django_media: {}
  
  production_redis_data: {}
  


services:
  django: &django
    build:
      context: .
      dockerfile: ./compose/production/django/Dockerfile

    image: bullion_dance_production_django
    volumes:
      - production_django_media:/app/bullion_dance/media
    depends_on:
      - postgres
      - redis
    env_file:
      - ./.envs/.production/.django
      - ./.envs/.production/.postgres
    command: /start

  postgres:
    build:
      context: .
      dockerfile: ./compose/production/postgres/Dockerfile
    image: bullion_dance_production_postgres
    volumes:
      - production_postgres_data:/var/lib/postgresql/data
      - production_postgres_data_backups:/backups
    env_file:
      - ./.envs/.production/.postgres

  traefik:
    build:
      context: .
      dockerfile: ./compose/production/traefik/Dockerfile
    image: bullion_dance_production_traefik
    depends_on:
      - django
    volumes:
      - production_traefik:/etc/traefik/acme
    ports:
      - '0.0.0.0:80:80'
      - '0.0.0.0:443:443'
      - '0.0.0.0:5555:5555'

  redis:
    image: docker.io/redis:7.2
    
    volumes:
      - production_redis_data:/data
    

  celeryworker:
    <<: *django
    image: bullion_dance_production_celeryworker
    command: /start-celeryworker

  celerybeat:
    <<: *django
    image: bullion_dance_production_celerybeat
    command: /start-celerybeat

  flower:
    <<: *django
    image: bullion_dance_production_flower
    command: /start-flower

  nginx:
    build:
      context: .
      dockerfile: ./compose/production/nginx/Dockerfile
    image: bullion_dance_production_nginx
    depends_on:
      - django
    volumes:
      - production_django_media:/usr/share/nginx/media:ro
