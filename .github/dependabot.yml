# Config for Dependabot updates. See Documentation here:
# https://docs.github.com/en/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  # Update GitHub actions in workflows
  - package-ecosystem: 'github-actions'
    directory: '/'
    # Every weekday
    schedule:
      interval: 'daily'
    groups:
      github-actions:
        patterns:
          - '*'

  # Enable version updates for Docker
  - package-ecosystem: 'docker'
    # Look for a `Dockerfile` in the `compose/local/django` directory
    directories:
      - 'compose/local/django/'
      - 'compose/local/docs/'
      - 'compose/production/django/'
    # Every weekday
    schedule:
      interval: 'daily'
    # Ignore minor version updates (3.10 -> 3.11) but update patch versions
    ignore:
      - dependency-name: '*'
        update-types:
          - 'version-update:semver-major'
          - 'version-update:semver-minor'
    groups:
      docker-python:
        patterns:
          - '*'


  - package-ecosystem: 'docker'
    # Look for a `Dockerfile` in the listed directories
    directories:
      - 'compose/local/node/'
      - 'compose/production/aws/'
      - 'compose/production/postgres/'
      - 'compose/production/traefik/'
      - 'compose/production/nginx/'
    # Every weekday
    schedule:
      interval: 'daily'

  # Enable version updates for Docker Compose files
  - package-ecosystem: 'docker-compose'
    directories:
      - '/'
    # Every weekday
    schedule:
      interval: 'daily'

  # Enable version updates for Python/Pip - Production
  - package-ecosystem: 'pip'
    # Look for a `requirements.txt` in the `root` directory
    # also 'setup.cfg', '.python-version' and 'requirements/*.txt'
    directory: '/'
    # Every weekday
    schedule:
      interval: 'daily'
    groups:
      python:
        update-types:
          - 'minor'
          - 'patch'
