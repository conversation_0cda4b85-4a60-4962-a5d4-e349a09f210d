name: <PERSON><PERSON>

# Enable Buildkit and let compose use it to speed up image building
env:
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1

on:
  pull_request:
    branches: ['main']
    paths-ignore: ['docs/**']

  push:
    branches: ['main']
    paths-ignore: ['docs/**']

concurrency:
  group: ${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  linter:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v5

      - name: Set up Python
        uses: actions/setup-python@v6
        with:
          python-version-file: '.python-version'
      - name: Run pre-commit
        uses: pre-commit/action@v3.0.1

  # With no caching at all the entire ci process takes 3m to complete!
  pytest:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code Repository
        uses: actions/checkout@v5

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and cache local backend
        uses: docker/bake-action@v6
        with:
          push: false
          load: true
          files: docker-compose.local.yml
          targets: django
          set: |
            django.cache-from=type=gha,scope=django-cached-tests
            django.cache-to=type=gha,scope=django-cached-tests,mode=max
            postgres.cache-from=type=gha,scope=postgres-cached-tests
            postgres.cache-to=type=gha,scope=postgres-cached-tests,mode=max

      - name: Build and cache docs
        uses: docker/bake-action@v6
        with:
          push: false
          load: true
          files: docker-compose.docs.yml
          set: |
            docs.cache-from=type=gha,scope=cached-docs
            docs.cache-to=type=gha,scope=cached-docs,mode=max

      - name: Check DB Migrations
        run: docker compose -f docker-compose.local.yml run --rm django python manage.py makemigrations --check

      - name: Run DB Migrations
        run: docker compose -f docker-compose.local.yml run --rm django python manage.py migrate

      - name: Run Django Tests
        run: docker compose -f docker-compose.local.yml run django pytest

      - name: Tear down the Stack
        run: docker compose -f docker-compose.local.yml down
