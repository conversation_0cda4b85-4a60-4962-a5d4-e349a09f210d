services:
  docs:
    image: bullion_dance_local_docs
    container_name: bullion_dance_local_docs
    build:
      context: .
      dockerfile: ./compose/local/docs/Dockerfile
    env_file:
      - ./.envs/.local/.django
    volumes:
      - /app/.venv
      - ./docs:/docs:z
      - ./config:/app/config:z
      - ./bullion_dance:/app/bullion_dance:z
    ports:
      - '9000:9000'
    command: /start-docs
