#!/usr/bin/env python
"""
测试过夜费计算逻辑的脚本
用于验证新的抵消状态分析是否正确工作
"""

import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# 设置 Django 环境
sys.path.append('/home/<USER>/dev/bullion_dance')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from bullion_dance.core.models import (
    TransactionAgency, TransactionUser, XAUSaleOrder, XAUBuyBackOrder, 
    XAUOffsetRelation, DailyBill
)

def test_overnight_fee_calculation():
    """测试过夜费计算逻辑"""
    print("=== 测试过夜费计算逻辑 ===\n")
    
    # 创建测试数据
    agency, _ = TransactionAgency.objects.get_or_create(name="测试代理机构")
    user, _ = TransactionUser.objects.get_or_create(
        name="测试用户",
        defaults={
            'agency': agency,
            'monthly_rebate_factor': Decimal('0.05'),
            'daily_deduction_factor': Decimal('0.02'),
            'xau_margin_factor': 1000,
            'xpt_margin_factor': 800,
            'xau_over_night_fee_factor': 50,
            'xpt_over_night_fee_factor': 40,
        }
    )
    
    # 清理之前的测试数据
    XAUOffsetRelation.objects.filter(
        sale_order__trans_user=user
    ).delete()
    XAUSaleOrder.objects.filter(trans_user=user).delete()
    XAUBuyBackOrder.objects.filter(trans_user=user).delete()
    
    # 创建测试订单
    today = date.today()
    
    # 销售订单1: 1000g，完全未抵消
    sale1 = XAUSaleOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('500.000'),
        weight=Decimal('1000.000')
    )
    
    # 销售订单2: 2000g，部分抵消（抵消500g）
    sale2 = XAUSaleOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('500.000'),
        weight=Decimal('2000.000')
    )
    
    # 销售订单3: 800g，完全抵消
    sale3 = XAUSaleOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('500.000'),
        weight=Decimal('800.000')
    )
    
    # 回购订单1: 500g，用于部分抵消sale2
    buyback1 = XAUBuyBackOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('510.000'),
        weight=Decimal('500.000')
    )
    
    # 回购订单2: 800g，用于完全抵消sale3
    buyback2 = XAUBuyBackOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('510.000'),
        weight=Decimal('800.000')
    )
    
    # 回购订单3: 300g，完全未抵消
    buyback3 = XAUBuyBackOrder.objects.create(
        trans_user=user,
        trans_date=today,
        locked_price=Decimal('510.000'),
        weight=Decimal('300.000')
    )
    
    # 创建抵消关系
    XAUOffsetRelation.objects.create(
        sale_order=sale2,
        buyback_order=buyback1,
        offset_weight=Decimal('500.000')
    )
    
    XAUOffsetRelation.objects.create(
        sale_order=sale3,
        buyback_order=buyback2,
        offset_weight=Decimal('800.000')
    )
    
    # 测试分析结果
    analysis = user.get_xau_holding_analysis(today, today)
    
    print("黄金持仓分析结果:")
    print(f"销售订单 - 完全抵消: {len(analysis['sales']['fully_offset_orders'])}笔")
    print(f"销售订单 - 部分抵消: {len(analysis['sales']['partially_offset_orders'])}笔")
    print(f"销售订单 - 完全未抵消: {len(analysis['sales']['unmatched_orders'])}笔")
    print(f"销售订单未抵消总重量: {analysis['sales']['total_unmatched_weight']}g")
    
    print(f"回购订单 - 完全抵消: {len(analysis['buybacks']['fully_offset_orders'])}笔")
    print(f"回购订单 - 部分抵消: {len(analysis['buybacks']['partially_offset_orders'])}笔")
    print(f"回购订单 - 完全未抵消: {len(analysis['buybacks']['unmatched_orders'])}笔")
    print(f"回购订单未抵消总重量: {analysis['buybacks']['total_unmatched_weight']}g")
    
    print(f"总持仓重量: {analysis['total_holding_weight']}g")
    
    # 验证计算结果
    expected_sales_unmatched = 1000 + 1500  # sale1完全未抵消1000g + sale2部分抵消剩余1500g
    expected_buybacks_unmatched = 300  # buyback3完全未抵消300g
    expected_total = expected_sales_unmatched + expected_buybacks_unmatched
    
    print(f"\n预期结果:")
    print(f"销售订单未抵消重量: {expected_sales_unmatched}g")
    print(f"回购订单未抵消重量: {expected_buybacks_unmatched}g")
    print(f"总持仓重量: {expected_total}g")
    
    # 验证
    assert analysis['sales']['total_unmatched_weight'] == expected_sales_unmatched
    assert analysis['buybacks']['total_unmatched_weight'] == expected_buybacks_unmatched
    assert analysis['total_holding_weight'] == expected_total
    
    print(f"\n✅ 测试通过！计算结果正确。")
    
    # 测试过夜费计算
    overnight_fee = user.calculate_overnight_fee(today, today)
    expected_fee = (expected_total / 1000) * user.xau_over_night_fee_factor
    
    print(f"\n过夜费计算:")
    print(f"计算结果: ¥{overnight_fee:.2f}")
    print(f"预期结果: ¥{expected_fee:.2f}")
    
    assert overnight_fee == expected_fee
    print(f"✅ 过夜费计算正确！")

if __name__ == "__main__":
    test_overnight_fee_calculation()
