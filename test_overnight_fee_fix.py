#!/usr/bin/env python
"""
测试修正后的过夜费计算逻辑
验证销售回购抵消后的过夜费计算是否正确
"""

import os
import sys
import django
from datetime import date, timedelta
from decimal import Decimal

# 设置 Django 环境
sys.path.append('/home/<USER>/dev/bullion_dance')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')
django.setup()

from bullion_dance.core.models import (
    TransactionAgency, TransactionUser, XAUSaleOrder, XAUBuyBackOrder, 
    XAUOffsetRelation, DailyBill
)

def test_overnight_fee_calculation():
    """测试过夜费计算逻辑"""
    
    # 创建测试用户
    agency, _ = TransactionAgency.objects.get_or_create(name='测试代理')
    user, _ = TransactionUser.objects.get_or_create(
        name='测试用户',
        defaults={
            'agency': agency,
            'monthly_rebate_factor': Decimal('0.01'),
            'daily_deduction_factor': Decimal('0.01'),
            'xau_margin_factor': 1000,
            'xpt_margin_factor': 1000,
            'xau_over_night_fee_factor': 10,  # 每kg每天10元
            'xpt_over_night_fee_factor': 10,
        }
    )
    
    # 清理之前的测试数据
    XAUOffsetRelation.objects.filter(
        sale_order__trans_user=user
    ).delete()
    XAUSaleOrder.objects.filter(trans_user=user).delete()
    XAUBuyBackOrder.objects.filter(trans_user=user).delete()
    DailyBill.objects.filter(transaction_user=user).delete()
    
    # 测试场景：
    # Day 1: 销售 1000g
    # Day 2: 销售 500g  
    # Day 3: 回购 600g，抵消第一笔销售的600g
    # Day 4: 回购 400g，抵消第一笔销售的400g
    # 
    # 预期过夜费（销售和回购都产生过夜费，抵消部分不产生）：
    # Day 1: 1000g(销售) * 10/1000 = 10元
    # Day 2: (1000g(销售) + 500g(销售)) * 10/1000 = 15元  
    # Day 3: (400g(销售未抵消) + 500g(销售) + 600g(回购)) * 10/1000 = 15元
    # Day 4: (0g(销售已完全抵消) + 500g(销售) + 200g(回购未抵消)) * 10/1000 = 7元
    # 总计: 10 + 15 + 15 + 7 = 47元
    
    day1 = date.today()
    day2 = day1 + timedelta(days=1)
    day3 = day1 + timedelta(days=2)
    day4 = day1 + timedelta(days=3)
    
    # Day 1: 销售 1000g
    sale1 = XAUSaleOrder.objects.create(
        trans_user=user,
        trans_date=day1,
        locked_price=Decimal('500.000'),
        weight=Decimal('1000.000')
    )
    
    # Day 2: 销售 500g
    sale2 = XAUSaleOrder.objects.create(
        trans_user=user,
        trans_date=day2,
        locked_price=Decimal('500.000'),
        weight=Decimal('500.000')
    )
    
    # Day 3: 回购 600g
    buyback1 = XAUBuyBackOrder.objects.create(
        trans_user=user,
        trans_date=day3,
        locked_price=Decimal('510.000'),
        weight=Decimal('600.000')
    )
    
    # Day 4: 回购 400g
    buyback2 = XAUBuyBackOrder.objects.create(
        trans_user=user,
        trans_date=day4,
        locked_price=Decimal('510.000'),
        weight=Decimal('400.000')
    )
    
    # 创建抵消关系（假设在各自的交易日创建）
    offset1 = XAUOffsetRelation.objects.create(
        sale_order=sale1,
        buyback_order=buyback1,
        offset_weight=Decimal('600.000')
    )
    # 模拟在day3创建
    offset1.created_at = offset1.created_at.replace(
        year=day3.year, month=day3.month, day=day3.day
    )
    offset1.save()
    
    offset2 = XAUOffsetRelation.objects.create(
        sale_order=sale1,
        buyback_order=buyback2,
        offset_weight=Decimal('400.000')
    )
    # 模拟在day4创建
    offset2.created_at = offset2.created_at.replace(
        year=day4.year, month=day4.month, day=day4.day
    )
    offset2.save()
    
    # 创建账单
    bill = DailyBill.objects.create(
        transaction_user=user,
        bill_start_date=day1,
        bill_end_date=day4,
        xau_internal_closing_price=Decimal('500.000'),
        xau_external_closing_price=Decimal('510.000')
    )
    
    # 测试每日持仓重量计算
    print("每日持仓重量计算:")
    print(f"Day 1 ({day1}): {bill.calculate_daily_holding_weight(day1)}g")
    print(f"Day 2 ({day2}): {bill.calculate_daily_holding_weight(day2)}g")
    print(f"Day 3 ({day3}): {bill.calculate_daily_holding_weight(day3)}g")
    print(f"Day 4 ({day4}): {bill.calculate_daily_holding_weight(day4)}g")
    
    # 测试过夜费计算
    overnight_fee = bill.xau_overnight_fee
    print(f"\n计算的过夜费: {overnight_fee}元")
    
    # 预期过夜费计算
    expected_fee = (
        Decimal('1000') / 1000 * 10 +  # Day 1: 1000g
        Decimal('1500') / 1000 * 10 +  # Day 2: 1500g
        Decimal('1500') / 1000 * 10 +  # Day 3: 1500g (400+500+600)
        Decimal('700') / 1000 * 10     # Day 4: 700g (0+500+200)
    )
    print(f"预期过夜费: {expected_fee}元")
    
    # 验证
    if abs(overnight_fee - expected_fee) < Decimal('0.01'):
        print("✅ 过夜费计算正确！")
        return True
    else:
        print("❌ 过夜费计算错误！")
        print(f"差异: {overnight_fee - expected_fee}元")
        return False

if __name__ == '__main__':
    success = test_overnight_fee_calculation()
    if success:
        print("\n🎉 所有测试通过！")
    else:
        print("\n💥 测试失败！")
        sys.exit(1)
